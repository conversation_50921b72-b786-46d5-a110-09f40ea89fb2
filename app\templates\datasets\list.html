{% extends "base.html" %}

{% block title %}My Datasets - Dataset Metadata Manager{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">My Datasets</h1>
        <a href="{{ url_for('datasets.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add New Dataset
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('datasets.list') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="category" class="form-label">Category</label>
                    <select name="category" id="category" class="form-select">
                        <option value="">All Categories</option>
                        <option value="education" {{ 'selected' if request.args.get('category') == 'education' else '' }}>Education</option>
                        <option value="health" {{ 'selected' if request.args.get('category') == 'health' else '' }}>Health</option>
                        <option value="agriculture" {{ 'selected' if request.args.get('category') == 'agriculture' else '' }}>Agriculture</option>
                        <option value="environment" {{ 'selected' if request.args.get('category') == 'environment' else '' }}>Environment</option>
                        <option value="social_science" {{ 'selected' if request.args.get('category') == 'social_science' else '' }}>Social Science</option>
                        <option value="economics" {{ 'selected' if request.args.get('category') == 'economics' else '' }}>Economics</option>
                        <option value="other" {{ 'selected' if request.args.get('category') == 'other' else '' }}>Other</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="data_type" class="form-label">Data Type</label>
                    <select name="data_type" id="data_type" class="form-select">
                        <option value="">All Types</option>
                        <option value="tabular" {{ 'selected' if request.args.get('data_type') == 'tabular' else '' }}>Tabular Data</option>
                        <option value="text" {{ 'selected' if request.args.get('data_type') == 'text' else '' }}>Textual Data</option>
                        <option value="image" {{ 'selected' if request.args.get('data_type') == 'image' else '' }}>Image Data</option>
                        <option value="time_series" {{ 'selected' if request.args.get('data_type') == 'time_series' else '' }}>Time Series</option>
                        <option value="geo" {{ 'selected' if request.args.get('data_type') == 'geo' else '' }}>Geospatial Data</option>
                        <option value="mixed" {{ 'selected' if request.args.get('data_type') == 'mixed' else '' }}>Mixed Data Types</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ 'selected' if request.args.get('status') == 'pending' else '' }}>Pending</option>
                        <option value="processing" {{ 'selected' if request.args.get('status') == 'processing' else '' }}>Processing</option>
                        <option value="completed" {{ 'selected' if request.args.get('status') == 'completed' else '' }}>Completed</option>
                        <option value="failed" {{ 'selected' if request.args.get('status') == 'failed' else '' }}>Failed</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-secondary w-100" id="filterButton">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Datasets Table -->
    <div class="card">
        <div class="card-body">
            {% if datasets %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Source</th>
                            <th>Category</th>
                            <th>Data Type</th>
                            <th>Status</th>
                            <th>Added</th>
                            <th class="text-center">Quality</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dataset in datasets %}
                        <tr>
                            <td>
                                <a href="{{ url_for('datasets.view', dataset_id=dataset.id) }}" class="text-decoration-none text-primary fw-medium">
                                    {{ dataset.title }}
                                </a>
                            </td>
                            <td>{{ dataset.source or 'N/A' }}</td>
                            <td>
                                {% if dataset.category %}
                                <span class="badge bg-secondary">{{ dataset.category }}</span>
                                {% else %}
                                <span class="text-muted">Not specified</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if dataset.data_type %}
                                <span class="badge bg-info">{{ dataset.data_type }}</span>
                                {% else %}
                                <span class="text-muted">Not specified</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if dataset.status == 'completed' %}
                                <span class="badge bg-success">Completed</span>
                                {% elif dataset.status == 'processing' %}
                                <span class="badge bg-warning">Processing</span>
                                {% elif dataset.status == 'pending' %}
                                <span class="badge bg-info">Pending</span>
                                {% elif dataset.status == 'failed' %}
                                <span class="badge bg-danger">Failed</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ dataset.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ dataset.created_at.strftime('%b %d, %Y') }}</td>
                            <td class="text-center">
                                {% if dataset.metadata %}
                                <div class="quality-score {{ 'quality-high' if dataset.metadata.quality_score >= 80 else ('quality-medium' if dataset.metadata.quality_score >= 50 else 'quality-low') }}">
                                    {{ dataset.metadata.quality_score|int }}
                                </div>
                                {% else %}
                                <span class="badge bg-secondary">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('datasets.view', dataset_id=dataset.id) }}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('datasets.edit', dataset_id=dataset.id) }}" class="btn btn-sm btn-outline-secondary" data-bs-toggle="tooltip" title="Edit Dataset">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if dataset.status == 'pending' %}
                                    <form method="POST" action="{{ url_for('datasets.start_processing', dataset_id=dataset.id) }}" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                        <button type="submit" class="btn btn-sm btn-outline-success" data-bs-toggle="tooltip" title="Start Processing">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                    {% if dataset.status == 'completed' %}
                                    <a href="{{ url_for('datasets.metadata', dataset_id=dataset.id) }}" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="View Metadata">
                                        <i class="fas fa-code"></i>
                                    </a>
                                    {% endif %}
                                    <form method="POST" action="{{ url_for('datasets.delete', dataset_id=dataset.id) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this dataset?')">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Delete Dataset">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="fas fa-database fa-4x text-muted"></i>
                </div>
                <h4>No datasets found</h4>
                <p class="text-muted">You haven't added any datasets yet, or none match your current filters.</p>
                <a href="{{ url_for('datasets.create') }}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus me-2"></i>Add New Dataset
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Pagination -->
    {% if pagination and pagination.pages > 1 %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('datasets.list', page=pagination.prev_num, **request.args) }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}

            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('datasets.list', page=page, **request.args) }}">{{ page }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <a class="page-link" href="#">{{ page }}</a>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#">...</a>
                </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('datasets.list', page=pagination.next_num, **request.args) }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter form loading
    const filterForm = document.querySelector('form[action*="list"]');
    const filterButton = document.getElementById('filterButton');

    if (filterForm && filterButton) {
        filterForm.addEventListener('submit', function(e) {
            const formData = new FormData(filterForm);
            const hasFilters = Array.from(formData.values()).some(value => value.trim() !== '');

            if (hasFilters) {
                const loaderId = window.smartLoader.show('general', {
                    title: '🔍 Filtering Datasets',
                    subtitle: 'Applying filters to dataset list...',
                    tips: 'This should only take a moment'
                });

                filterButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Filtering...';
                filterButton.disabled = true;

                window.currentFilterLoader = loaderId;
            }
        });
    }

    // Processing button loading
    const processingForms = document.querySelectorAll('form[action*="start_processing"]');
    processingForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const datasetRow = this.closest('tr');
            const datasetTitle = datasetRow ? datasetRow.querySelector('a').textContent.trim() : 'dataset';

            const loaderId = window.smartLoader.showProcessingLoader(datasetTitle);

            const button = this.querySelector('button[type="submit"]');
            if (button) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                button.disabled = true;
            }

            window.currentProcessingLoader = loaderId;
        });
    });

    // Pagination loading
    const paginationLinks = document.querySelectorAll('.pagination .page-link[href]');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && href !== '#') {
                const loaderId = window.smartLoader.show('general', {
                    title: '📄 Loading Page',
                    subtitle: 'Loading dataset list...',
                    tips: 'Fetching your datasets'
                });

                window.currentPageLoader = loaderId;
            }
        });
    });

    // Dataset view links loading
    const datasetLinks = document.querySelectorAll('a[href*="datasets/view"]');
    datasetLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const datasetTitle = this.textContent.trim();

            const loaderId = window.smartLoader.show('general', {
                title: '📊 Loading Dataset',
                subtitle: `Opening "${datasetTitle}"...`,
                tips: 'Loading dataset details and metadata'
            });

            window.currentDatasetLoader = loaderId;
        });
    });

    // Metadata view links loading
    const metadataLinks = document.querySelectorAll('a[href*="metadata"]');
    metadataLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const loaderId = window.smartLoader.show('processing', {
                title: '📋 Loading Metadata',
                subtitle: 'Fetching dataset metadata and quality information...',
                tips: 'This includes FAIR compliance and quality scores'
            });

            window.currentMetadataLoader = loaderId;
        });
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.currentFilterLoader) {
        window.smartLoader.hide(window.currentFilterLoader);
    }
    if (window.currentProcessingLoader) {
        window.smartLoader.hide(window.currentProcessingLoader);
    }
    if (window.currentPageLoader) {
        window.smartLoader.hide(window.currentPageLoader);
    }
    if (window.currentDatasetLoader) {
        window.smartLoader.hide(window.currentDatasetLoader);
    }
    if (window.currentMetadataLoader) {
        window.smartLoader.hide(window.currentMetadataLoader);
    }
});
</script>
{% endblock %}